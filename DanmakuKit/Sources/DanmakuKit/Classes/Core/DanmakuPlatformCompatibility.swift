//
//  DanmakuPlatformCompatibility.swift
//  DanmakuKit
//
//  Created by chaoqun Agent on 2025/8/23.
//  Platform compatibility layer for iOS and macOS
//

import Foundation

#if os(iOS) || os(tvOS)
import UIKit

// MARK: - iOS/tvOS Platform Aliases
public typealias DanmakuView_BaseView = UIView
public typealias DanmakuColor = UIColor
public typealias DanmakuScreen = UIScreen
public typealias DanmakuBezierPath = UIBezierPath
public typealias DanmakuFont = UIFont
public typealias DanmakuImage = UIImage
public typealias DanmakuEvent = UIEvent

// MARK: - iOS/tvOS Platform Extensions
extension DanmakuView_BaseView {
    var danmaku_isFlipped: Bool { return false }
    var danmaku_wantsLayer: Bool {
        get { return layer != nil }
        set { /* UIView always has layer */ }
    }
    
    func danmaku_setNeedsLayout() {
        setNeedsLayout()
    }
    
    func danmaku_layoutIfNeeded() {
        layoutIfNeeded()
    }
}

extension DanmakuScreen {
    static var danmaku_mainScale: CGFloat {
        return UIScreen.main.scale
    }
}

extension DanmakuColor {
    static var danmaku_white: DanmakuColor {
        return UIColor.white
    }
    
    static var danmaku_clear: DanmakuColor {
        return UIColor.clear
    }
}

// MARK: - iOS Graphics Context Helpers
struct DanmakuGraphicsContext {
    static func beginImageContext(size: CGSize, opaque: Bool, scale: CGFloat) {
        UIGraphicsBeginImageContextWithOptions(size, opaque, scale)
    }
    
    static func getCurrentContext() -> CGContext? {
        return UIGraphicsGetCurrentContext()
    }
    
    static func getImageFromCurrentContext() -> DanmakuImage? {
        return UIGraphicsGetImageFromCurrentImageContext()
    }
    
    static func endImageContext() {
        UIGraphicsEndImageContext()
    }
    
    static func createContext(size: CGSize, scale: CGFloat, opaque: Bool) -> CGContext? {
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let alphaInfo: CGImageAlphaInfo = opaque ? .noneSkipLast : .premultipliedLast
        return CGContext(data: nil, 
                        width: Int(size.width * scale), 
                        height: Int(size.height * scale), 
                        bitsPerComponent: 8, 
                        bytesPerRow: 0, 
                        space: colorSpace, 
                        bitmapInfo: alphaInfo.rawValue)
    }
}

#elseif os(macOS)
import Cocoa

// MARK: - macOS Platform Aliases
public typealias DanmakuView_BaseView = NSView
public typealias DanmakuColor = NSColor
public typealias DanmakuScreen = NSScreen
public typealias DanmakuBezierPath = NSBezierPath
public typealias DanmakuFont = NSFont
public typealias DanmakuImage = NSImage
public typealias DanmakuEvent = NSEvent

// MARK: - macOS Platform Extensions
extension DanmakuView_BaseView {
    var danmaku_isFlipped: Bool { return isFlipped }
    var danmaku_wantsLayer: Bool {
        get { return wantsLayer }
        set { wantsLayer = newValue }
    }
    
    func danmaku_setNeedsLayout() {
        needsLayout = true
    }
    
    func danmaku_layoutIfNeeded() {
        layoutSubtreeIfNeeded()
    }
}

extension DanmakuScreen {
    static var danmaku_mainScale: CGFloat {
        return NSScreen.main?.backingScaleFactor ?? 1.0
    }
}

extension DanmakuColor {
    static var danmaku_white: DanmakuColor {
        return NSColor.white
    }
    
    static var danmaku_clear: DanmakuColor {
        return NSColor.clear
    }
}

// MARK: - macOS Graphics Context Helpers
struct DanmakuGraphicsContext {
    static func beginImageContext(size: CGSize, opaque: Bool, scale: CGFloat) {
        // macOS doesn't have UIGraphicsBeginImageContextWithOptions
        // We'll handle this in the context creation
    }
    
    static func getCurrentContext() -> CGContext? {
        return NSGraphicsContext.current?.cgContext
    }
    
    static func getImageFromCurrentContext() -> DanmakuImage? {
        // This will be handled differently in macOS
        return nil
    }
    
    static func endImageContext() {
        // No-op for macOS
    }
    
    static func createContext(size: CGSize, scale: CGFloat, opaque: Bool) -> CGContext? {
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let alphaInfo: CGImageAlphaInfo = opaque ? .noneSkipLast : .premultipliedLast
        return CGContext(data: nil, 
                        width: Int(size.width * scale), 
                        height: Int(size.height * scale), 
                        bitsPerComponent: 8, 
                        bytesPerRow: 0, 
                        space: colorSpace, 
                        bitmapInfo: alphaInfo.rawValue)
    }
}

#endif

// MARK: - Common Platform Extensions
extension CGRect {
    var danmaku_center: CGPoint {
        return CGPoint(x: midX, y: midY)
    }
}

extension CGSize {
    static var danmaku_zero: CGSize {
        return CGSize.zero
    }
}

extension CGPoint {
    static var danmaku_zero: CGPoint {
        return CGPoint.zero
    }
}

// MARK: - SwiftUI Compatibility
#if canImport(SwiftUI)
import SwiftUI

#if os(iOS) || os(tvOS)
@available(iOS 14.0, tvOS 14.0, *)
public typealias DanmakuViewRepresentable = UIViewRepresentable
@available(iOS 13.0, tvOS 13.0, *)
public typealias DanmakuViewRepresentableContext = UIViewRepresentableContext

@available(iOS 14.0, tvOS 14.0, *)
extension DanmakuViewRepresentable {
    public typealias DanmakuViewType = UIView
}
#elseif os(macOS)
@available(macOS 10.15, *)
public typealias DanmakuViewRepresentable = NSViewRepresentable
@available(macOS 10.15, *)
public typealias DanmakuViewRepresentableContext = NSViewRepresentableContext

@available(macOS 10.15, *)
extension DanmakuViewRepresentable {
    public typealias DanmakuViewType = NSView
}
#endif

#endif
